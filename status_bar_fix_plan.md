# Status Bar Color Fix Plan

## Issue
The Customer Support screen (which appears to be a webview) has a blank white status bar instead of using the primary blue color that matches the toolbar.

## Root Cause
The `WebviewActivity` extends `BaseWebviewActivity` but doesn't explicitly set the status bar color, leaving it as the default white/transparent color.

## Solution
Add status bar color configuration to the `WebviewActivity.onCreate()` method, similar to how it's done in `CustomWebviewActivity`.

## Implementation Steps

1. **Add status bar color configuration** in `WebviewActivity.onCreate()` method
   - Import `androidx.core.content.ContextCompat` if not already imported
   - Add `window?.statusBarColor = ContextCompat.getColor(this, R.color.colorPrimary)` after `super.onCreate(savedInstanceState)`

2. **Test the fix**
   - Build the app with stgDebug variant
   - Navigate to Customer Support screen
   - Verify status bar now shows blue color matching the toolbar

## Files to Modify
- `app/src/main/java/com/bukuwarung/activities/WebviewActivity.kt`

## Reference Implementation
The `CustomWebviewActivity` already has the correct implementation:
```kotlin
// Set status bar color to match the primary color
window?.statusBarColor = ContextCompat.getColor(this, R.color.colorPrimary)
```

## Color Resource
- `colorPrimary` is defined as `#0091ff` in `app/src/main/res/values/colors.xml`
