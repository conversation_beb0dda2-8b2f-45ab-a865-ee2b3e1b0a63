package com.bukuwarung.activities.experiments

import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.webkit.*
import androidx.core.content.ContextCompat
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.BOOK_NAME
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.ActivityWebviewBinding
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.AuthHelper
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.*
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import org.json.JSONObject

/*
Custom webview activity is created to avoid too many webview activities for each mx feature.
It's supporting all functions needed for daily business update, story, loyalty, more methods can be added for future use cases.
 */
class CustomWebviewActivity : BaseWebviewActivity() {

    private lateinit var binding: ActivityWebviewBinding
    lateinit var bookId: String
    var bookName: String = ""
    var entryPoint: String = ""
    var interfaceName: String = "JsInterface"

    private lateinit var progressDialog: ProgressDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        this.requestWindowFeature(Window.FEATURE_NO_TITLE)
        super.onCreate(savedInstanceState)
        binding = ActivityWebviewBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val propBuilder = PropBuilder()
        AuthHelper.newSession()
        initWebview()
        if (intent.hasExtra("entry_point")) {
            propBuilder.put(AnalyticsConst.ENTRY_POINT, intent.getStringExtra("entry_point"))
            entryPoint = intent.getStringExtra("entry_point") ?: ""
        }
        if (intent.hasExtra(BOOK_NAME)) {
            bookName = intent.getStringExtra(BOOK_NAME) ?: ""
        }
        bookId = User.getBusinessId()
        if (!intent.getBooleanExtra(SHOW_TOOLBAR, true)) {
            getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            )
            binding.toolbar.visibility = View.GONE
        } else {
            // Set status bar color to match the primary color
            window?.statusBarColor = ContextCompat.getColor(this, R.color.colorPrimary)
        }
        if (bookId.isNotNullOrEmpty()) {
            bookName = BusinessRepository.getInstance(this).getBusinessByIdSync(bookId).businessName
            intent.getStringExtra(FEATURE_ID)?.let { setupWebViewClient(it) }
            webView?.addJavascriptInterface(JsObject(this), interfaceName)
        }

    }

    fun initWebview() {
        progressDialog = ComponentUtil.getProgressDialog(this, getString(R.string.please_wait), false)
        progressDialog.show()
    }

    override fun onBackPressed() {
        try {
            if (webView?.canGoBack()!!) {
                webView?.goBack()
            } else {
                finish()
            }
        } catch (ex: Exception) {
            finish()
        }
    }

    override fun hideToolBar(): Boolean {
        return true;
    }

    private fun hideLoader() {
        if (!this.isFinishing && progressDialog.isShowing)
            progressDialog.dismiss()
    }

    override fun onDestroy() {
        hideLoader()
        super.onDestroy()
    }

    private fun saveImageAndShare() {
        ImageUtils.saveLayoutConvertedImage(this.findViewById(R.id.webView), true)
        val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(this.findViewById(R.id.webView), false)
        val shareLayoutImage = ShareLayoutImage(getString(R.string.share_with), this, null, null, false, false)
        saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
    }

    private fun openActivity(activity: String, parameter: String, title: String) {
        val intent = Intent(this, Class.forName(activity))
        if (!parameter.equals("undefined", true)) {
            val jsonObject = JSONObject(parameter)
            val keys = jsonObject.keys()
            while (keys.hasNext()) {
                val key = keys.next()
                intent.putExtra(key, jsonObject[key].toString())
            }
        }
        startActivity(intent)
    }

    private fun setupWebViewClient(featureId: String) {
        if (featureId == "year_end_review") {
            FeaturePrefManager.getInstance().hasSeenYearReview(true)
            val webViewClient = CustomWebviewClient(bookId, this, bookName)
            webView?.let {
                it?.webViewClient = webViewClient
            }
        } else {
            val webViewClient = CustomWebviewClient(bookId, this, bookName, featureId)
            webView?.let {
                it?.webViewClient = webViewClient
            }
        }
    }

    companion object {

        private val ENTRY_POINT: String = "entry_point"
        private val SHOW_TOOLBAR: String = "show_toolbar"
        private val FEATURE_ID: String = "feature_id"

        fun createIntent(origin: Context?, link: String?, title: String?, showToolbar: Boolean = true, featuerId: String, entryPoint: String, bookName: String = ""): Intent {
            val intent = Intent(origin, CustomWebviewActivity::class.java)
            intent.putExtra(LINK, link)
            intent.putExtra(TITLE, title)
            intent.putExtra(SHOW_TOOLBAR, showToolbar)
            intent.putExtra(FEATURE_ID, featuerId)
            intent.putExtra(ENTRY_POINT, entryPoint)
            intent.putExtra(BOOK_NAME, bookName)
            return intent
        }
    }

    override fun getLink(): String? {
        return intent.getStringExtra(LINK)
    }

    override fun getTitleText(): String? {
        return intent.getStringExtra(TITLE)
    }

    override fun getToolbarColor(): Int {
        return R.color.colorPrimary
    }

    override fun getUserAgent(): String? {
        return null
    }

    override fun getDeeplinkScheme(): String {
        return BuildConfig.DEEPLINK_SCHEME
    }

    override fun allowDebug(): Boolean {
        return false
    }

    override fun getAppToken(): String? {
        return SessionManager.getInstance().bukuwarungToken
    }

    override fun getUserId(): String? {
        return SessionManager.getInstance().userId
    }


    class CustomWebviewClient(val bookId: String, val context: Context, val businessName: String = "", val featureId: String = "") : WebViewClient() {

        override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError) {
            val activity = context as CustomWebviewActivity
            activity.binding.errorStateImg.setImageResource(R.drawable.ic_no_inet)
            activity.binding.errorState.visibility = View.VISIBLE
            activity.webView?.visibility = View.GONE
        }

        override fun shouldOverrideUrlLoading(
            view: WebView?,
            request: WebResourceRequest?
        ): Boolean {
            var url = request?.url
            if (url.toString()?.contains(BuildConfig.DEEPLINK_URL)!!) {
                view!!.context.startActivity(Intent(Intent.ACTION_VIEW, url))
                return true
            }

            view!!.loadUrl(url.toString())
            return false
        }

        override fun onPageFinished(view: WebView, url: String) {
            super.onPageFinished(view, url)
            (context as CustomWebviewActivity).hideLoader()
            val sessionManager = SessionManager.getInstance()
            val token = "Bearer " + sessionManager.bukuwarungToken

            // supply token with business name for year end review
            if (featureId == "loyalty_account") {
                val script = StringBuilder("javascript:supplyToken(")
                script.append("'").append(token).append("'").append(",")
                script.append("'").append(User.getUserId()).append("'").append(",")
                script.append("'").append(!OnboardingPrefManager.getInstance().getHasFinishedForId("membership-intro")).append("'").append(")")
                view.evaluateJavascript(script.toString(), null)
            } else {
                if (businessName.isNotNullOrEmpty()) {
                    val script = StringBuilder("javascript:supplyToken(")
                    script.append("'").append(token).append("'").append(",")
                    script.append("'").append(businessName).append("'").append(")")
                    view.evaluateJavascript(script.toString(), null)
                } else {
                    val script = StringBuilder("javascript:supplyToken(")
                    script.append("'").append(token).append("'").append(",")
                    script.append("'").append("-").append("'").append(")")
                    view.evaluateJavascript(script.toString(), null)
                }
            }

        }
    }

    internal class JsObject(var leaderboardWebviewActivity: CustomWebviewActivity) {
        @JavascriptInterface
        open fun onBackPressed() {
            leaderboardWebviewActivity.onBackPressed()
        }

        @JavascriptInterface
        open fun shareCalled() {
            leaderboardWebviewActivity.saveImageAndShare()
        }

        @JavascriptInterface
        open fun timerFinished() {
            leaderboardWebviewActivity.onBackPressed()
        }

        @JavascriptInterface
        open fun trackEvent(eventName: String = "", eventProp: String = "") {
            if (eventProp.isNotNullOrEmpty()) {
                AppAnalytics.trackEvent(eventName, PropBuilder().stringToProp(eventProp))
            } else {
                AppAnalytics.trackEvent(eventName)
            }
        }

        @JavascriptInterface
        open fun trackUserProprty(propName: String = "", propValue: String = "") {
            AppAnalytics.setUserProperty(propName, propValue)
        }

        @JavascriptInterface
        open fun getAppToken(): String? {
            return SessionManager.getInstance().bukuwarungToken
        }

        @JavascriptInterface
        open fun getUserId(): String? {
            return SessionManager.getInstance().userId
        }

        @JavascriptInterface
        open fun getAppVersionCode(): String? {
            return SetupManager.getInstance().getInstalledVersion().toString();
        }

        @JavascriptInterface
        open fun getAppVersionName(): String? {
            return SetupManager.getInstance().getInstallVersionName().toString();
        }

        @JavascriptInterface
        open fun isFeatureShown(id: String = ""): String? {
            if (id.isNullOrEmpty()) return false.toString();
            return OnboardingPrefManager.getInstance().getHasFinishedForId(id).toString();
        }

        @JavascriptInterface
        open fun setFeatureShown(id: String = "") {
            OnboardingPrefManager.getInstance().setHasFinishedForId(id);
        }

        @JavascriptInterface
        open fun isSaldoActivated(): String? {
            return PaymentPrefManager.getInstance().getHasActivatedSaldo().toString();
        }

        @JavascriptInterface
        open fun getEntryPoint(): String? {
            return leaderboardWebviewActivity.entryPoint
        }

        @JavascriptInterface
        open fun openActivity(activity: String = "", parameter: String, title: String) {
            leaderboardWebviewActivity.openActivity(activity, parameter, title)
        }

        @JavascriptInterface
        open fun openWhatsappWithMessage(phoneNumber: String = "", message: String = "") {
            leaderboardWebviewActivity.startActivity(
                Intent.createChooser(
                    ShareUtils.getShareUriOnWhatsappIntentWithPackage(
                        "com.whatsapp",
                        null,
                        phoneNumber,
                        message
                    ), "Choose an app"
                )
            )
        }

        @JavascriptInterface
        open fun copyToClipboard(text: String = "", toastText:String = "Teks Tersalin!") {
            Utility.copyToClipboard(text,leaderboardWebviewActivity,toastText)
        }
    }
}